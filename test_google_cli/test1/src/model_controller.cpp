#include "model_controller.h"
#include "camera.h"
#include "logger.h"
#include <GLFW/glfw3.h>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/type_ptr.hpp>
#include <algorithm>
#include <cmath>
#include <limits>

ModelController::ModelController(Camera* camera, int screenWidth, int screenHeight)
    : camera(camera), screenWidth(screenWidth), screenHeight(screenHeight),
      enableWindowBoundaries(true), enableCharacterCollision(false), selectedModelIndex(-1), dragging(false),
      dragOffset(0.0f), lastMousePos(0.0f), dragPlaneNormal(0.0f, 0.0f, 1.0f), dragPlaneDistance(0.0f) {
    LOG_INFO("ModelController initialized with screen size: " + std::to_string(screenWidth) + "x" + std::to_string(screenHeight));
    LOG_INFO("Window boundaries enabled: " + std::string(enableWindowBoundaries ? "true" : "false"));
}

ModelController::~ModelController() {
    LOG_INFO("ModelController destroyed");
}

void ModelController::addModel(const glm::vec3& position, const glm::vec3& scale, float boundingRadius) {
    DraggableModel model;
    model.position = position;
    model.scale = scale;
    model.boundingRadius = boundingRadius;
    updateModelMatrix(model);
    
    models.push_back(model);
    LOG_INFO("Added model at position (" + std::to_string(position.x) + ", " + 
             std::to_string(position.y) + ", " + std::to_string(position.z) + ")");
}

void ModelController::removeModel(int modelIndex) {
    if (modelIndex >= 0 && modelIndex < static_cast<int>(models.size())) {
        models.erase(models.begin() + modelIndex);
        if (selectedModelIndex == modelIndex) {
            selectedModelIndex = -1;
            dragging = false;
        } else if (selectedModelIndex > modelIndex) {
            selectedModelIndex--;
        }
        LOG_INFO("Removed model at index " + std::to_string(modelIndex));
    }
}

DraggableModel* ModelController::getModel(int index) {
    if (index >= 0 && index < static_cast<int>(models.size())) {
        return &models[index];
    }
    return nullptr;
}

void ModelController::addBoxObstacle(const glm::vec3& position, const glm::vec3& size) {
    obstacles.emplace_back(position, size);
    LOG_INFO("Added box obstacle at (" + std::to_string(position.x) + ", " + 
             std::to_string(position.y) + ", " + std::to_string(position.z) + ")");
}

void ModelController::addSphereObstacle(const glm::vec3& position, float radius) {
    obstacles.emplace_back(position, radius);
    LOG_INFO("Added sphere obstacle at (" + std::to_string(position.x) + ", " + 
             std::to_string(position.y) + ", " + std::to_string(position.z) + ") with radius " + std::to_string(radius));
}

void ModelController::clearObstacles() {
    obstacles.clear();
    LOG_INFO("Cleared all obstacles");
}

void ModelController::handleMouseButton(GLFWwindow* window, int button, int action, int mods) {
    (void)mods; // Suppress unused parameter warning
    
    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            double mouseX, mouseY;
            glfwGetCursorPos(window, &mouseX, &mouseY);
            
            int pickedModel = pickModel(mouseX, mouseY);
            if (pickedModel >= 0) {
                selectedModelIndex = pickedModel;
                models[selectedModelIndex].isSelected = true;
                startDragging(selectedModelIndex, mouseX, mouseY);
                LOG_INFO("Selected and started dragging model " + std::to_string(selectedModelIndex));
            } else {
                // Deselect all models
                for (auto& model : models) {
                    model.isSelected = false;
                }
                selectedModelIndex = -1;
                stopDragging();
            }
        } else if (action == GLFW_RELEASE) {
            if (dragging) {
                stopDragging();
                LOG_INFO("Stopped dragging model");
            }
        }
    }
}

void ModelController::handleMouseMove(GLFWwindow* window, double xpos, double ypos) {
    (void)window; // Suppress unused parameter warning
    
    if (dragging && selectedModelIndex >= 0) {
        updateDragging(xpos, ypos);
    }
    
    lastMousePos = glm::vec2(xpos, ypos);
}

void ModelController::handleKeyboard(GLFWwindow* window, float deltaTime) {
    (void)deltaTime; // Suppress unused parameter warning
    
    if (selectedModelIndex >= 0) {
        DraggableModel& model = models[selectedModelIndex];
        float moveSpeed = 2.0f * deltaTime;
        
        glm::vec3 newPosition = model.position;
        bool hasMovement = false;

        // Move selected model with arrow keys
        if (glfwGetKey(window, GLFW_KEY_UP) == GLFW_PRESS) {
            newPosition.y += moveSpeed;
            hasMovement = true;
        }
        if (glfwGetKey(window, GLFW_KEY_DOWN) == GLFW_PRESS) {
            newPosition.y -= moveSpeed;
            hasMovement = true;
        }
        if (glfwGetKey(window, GLFW_KEY_LEFT) == GLFW_PRESS) {
            newPosition.x -= moveSpeed;
            hasMovement = true;
        }
        if (glfwGetKey(window, GLFW_KEY_RIGHT) == GLFW_PRESS) {
            newPosition.x += moveSpeed;
            hasMovement = true;
        }

        // Only run collision resolution if there's actual movement
        if (hasMovement && newPosition != model.position) {
            // Check for collisions and resolve them
            float effectiveRadius = model.boundingRadius * glm::length(model.scale);
            glm::vec3 resolvedPosition = resolveCollision(model.position, newPosition, effectiveRadius);

            // Check character-to-character collision
            if (enableCharacterCollision) {
                resolvedPosition = resolveCharacterCollision(selectedModelIndex, model.position, resolvedPosition, effectiveRadius);
            }

            // Also check window boundaries if enabled
            if (enableWindowBoundaries) {
                resolvedPosition = resolveWindowBoundaryCollision(model.position, resolvedPosition, effectiveRadius);
            }

            if (resolvedPosition != model.position) {
                model.position = resolvedPosition;
                updateModelMatrix(model);
            }
        }
    }
}

void ModelController::update(float deltaTime) {
    (void)deltaTime; // Suppress unused parameter warning
    
    // Update any time-based animations or effects here
    // For now, just ensure all model matrices are up to date
    for (auto& model : models) {
        updateModelMatrix(model);
    }
}

glm::mat4 ModelController::getModelMatrix(int modelIndex) const {
    if (modelIndex >= 0 && modelIndex < static_cast<int>(models.size())) {
        return models[modelIndex].modelMatrix;
    }
    return glm::mat4(1.0f);
}

glm::vec3 ModelController::getModelPosition(int modelIndex) const {
    if (modelIndex >= 0 && modelIndex < static_cast<int>(models.size())) {
        return models[modelIndex].position;
    }
    return glm::vec3(0.0f);
}

bool ModelController::checkCharacterCollision(int modelIndex, const glm::vec3& position, float radius) const {
    if (!enableCharacterCollision) {
        return false;
    }

    // Check collision with all other models
    for (int i = 0; i < static_cast<int>(models.size()); ++i) {
        if (i == modelIndex) {
            continue; // Skip self
        }

        const DraggableModel& otherModel = models[i];
        float otherRadius = otherModel.boundingRadius * glm::length(otherModel.scale);
        float distance = glm::length(position - otherModel.position);

        if (distance < (radius + otherRadius)) {
            return true; // Collision detected
        }
    }

    return false;
}

glm::vec3 ModelController::resolveCharacterCollision(int modelIndex, const glm::vec3& oldPosition, const glm::vec3& newPosition, float radius) const {
    if (!enableCharacterCollision) {
        return newPosition;
    }

    glm::vec3 resolvedPosition = newPosition;
    float originalZ = newPosition.z; // Preserve the original z-coordinate

    // Check collision with all other models
    for (int i = 0; i < static_cast<int>(models.size()); ++i) {
        if (i == modelIndex) {
            continue; // Skip self
        }

        const DraggableModel& otherModel = models[i];
        float otherRadius = otherModel.boundingRadius * glm::length(otherModel.scale);
        float combinedRadius = radius + otherRadius;

        // Only check collision in XY plane (ignore z-coordinate)
        glm::vec2 resolvedPos2D = glm::vec2(resolvedPosition.x, resolvedPosition.y);
        glm::vec2 otherPos2D = glm::vec2(otherModel.position.x, otherModel.position.y);
        float distance = glm::length(resolvedPos2D - otherPos2D);

        if (distance < combinedRadius) {
            // Collision detected, push the model away in XY plane only
            glm::vec2 direction2D = resolvedPos2D - otherPos2D;

            if (glm::length(direction2D) > 0.001f) {
                direction2D = glm::normalize(direction2D);
                glm::vec2 newPos2D = otherPos2D + direction2D * combinedRadius;
                resolvedPosition.x = newPos2D.x;
                resolvedPosition.y = newPos2D.y;
                // Keep original z-coordinate
                resolvedPosition.z = originalZ;
            } else {
                // If positions are identical, push in a default direction (XY only)
                resolvedPosition.x = otherModel.position.x + combinedRadius;
                resolvedPosition.y = otherModel.position.y;
                // Keep original z-coordinate
                resolvedPosition.z = originalZ;
            }
        }
    }

    return resolvedPosition;
}

void ModelController::setScreenSize(int width, int height) {
    screenWidth = width;
    screenHeight = height;
    LOG_INFO("Screen size updated to " + std::to_string(width) + "x" + std::to_string(height));
}

glm::vec3 ModelController::calculateMouseRay(double mouseX, double mouseY) {
    // Convert screen coordinates to world ray direction
    float x = (2.0f * mouseX) / screenWidth - 1.0f;
    float y = 1.0f - (2.0f * mouseY) / screenHeight;

    glm::vec4 rayClip = glm::vec4(x, y, -1.0f, 1.0f);

    glm::mat4 projection = glm::perspective(glm::radians(camera->Zoom),
                                          (float)screenWidth / (float)screenHeight,
                                          0.1f, 100.0f);

    glm::vec4 rayEye = glm::inverse(projection) * rayClip;
    rayEye = glm::vec4(rayEye.x, rayEye.y, -1.0f, 0.0f);

    glm::vec4 rayWorld = glm::inverse(camera->GetViewMatrix()) * rayEye;
    return glm::normalize(glm::vec3(rayWorld));
}

glm::vec3 ModelController::screenToWorld(double mouseX, double mouseY, float depth) {
    glm::vec3 rayDirection = calculateMouseRay(mouseX, mouseY);
    return camera->Position + rayDirection * depth;
}

bool ModelController::rayIntersectsSphere(const glm::vec3& rayOrigin, const glm::vec3& rayDirection,
                                        const glm::vec3& sphereCenter, float sphereRadius, float& distance) {
    glm::vec3 oc = rayOrigin - sphereCenter;
    float a = glm::dot(rayDirection, rayDirection);
    float b = 2.0f * glm::dot(oc, rayDirection);
    float c = glm::dot(oc, oc) - sphereRadius * sphereRadius;

    float discriminant = b * b - 4 * a * c;

    if (discriminant < 0) {
        return false;
    }

    float t1 = (-b - sqrt(discriminant)) / (2.0f * a);
    float t2 = (-b + sqrt(discriminant)) / (2.0f * a);

    if (t1 > 0) {
        distance = t1;
        return true;
    } else if (t2 > 0) {
        distance = t2;
        return true;
    }

    return false;
}

bool ModelController::checkCollision(const glm::vec3& position, float radius) const {
    for (const auto& obstacle : obstacles) {
        if (obstacle.radius > 0.0f) {
            // Sphere-sphere collision
            float distance = glm::length(position - obstacle.position);
            if (distance < radius + obstacle.radius) {
                return true;
            }
        } else {
            // Sphere-box collision
            glm::vec3 closest = glm::clamp(position, 
                                         obstacle.position - obstacle.size,
                                         obstacle.position + obstacle.size);
            float distance = glm::length(position - closest);
            if (distance < radius) {
                return true;
            }
        }
    }
    return false;
}

glm::vec3 ModelController::resolveCollision(const glm::vec3& oldPosition, const glm::vec3& newPosition, float radius) const {
    if (!checkCollision(newPosition, radius)) {
        return newPosition;
    }
    
    // Try to slide along obstacles
    glm::vec3 direction = glm::normalize(newPosition - oldPosition);
    glm::vec3 resolvedPosition = oldPosition;
    
    // Try moving in individual axes
    glm::vec3 testPos = oldPosition;
    testPos.x = newPosition.x;
    if (!checkCollision(testPos, radius)) {
        resolvedPosition.x = newPosition.x;
    }
    
    testPos = resolvedPosition;
    testPos.y = newPosition.y;
    if (!checkCollision(testPos, radius)) {
        resolvedPosition.y = newPosition.y;
    }
    
    testPos = resolvedPosition;
    testPos.z = newPosition.z;
    if (!checkCollision(testPos, radius)) {
        resolvedPosition.z = newPosition.z;
    }
    
    return resolvedPosition;
}

bool ModelController::checkWindowBoundaryCollision(const glm::vec3& position, float radius) const {
    if (!enableWindowBoundaries) {
        return false;
    }

    // Project the 3D position to screen coordinates to check boundaries
    glm::mat4 view = camera->GetViewMatrix();
    glm::mat4 projection = glm::perspective(glm::radians(camera->Zoom),
                                          (float)screenWidth / (float)screenHeight,
                                          0.1f, 100.0f);
    glm::mat4 model = glm::mat4(1.0f);

    glm::vec4 viewport = glm::vec4(0, 0, screenWidth, screenHeight);
    glm::vec3 screenPos = glm::project(position, model * view, projection, viewport);

    // Calculate accurate screen space radius based on distance from camera
    float distanceFromCamera = glm::length(position - camera->Position);
    float fovRadians = glm::radians(camera->Zoom);
    float screenRadius = (radius * screenHeight) / (2.0f * distanceFromCamera * tan(fovRadians / 2.0f));

    // Add a small buffer to ensure the model stays fully visible
    float buffer = screenRadius * 0.1f;
    screenRadius += buffer;

    return (screenPos.x - screenRadius < 0 ||
            screenPos.x + screenRadius > screenWidth ||
            screenPos.y - screenRadius < 0 ||
            screenPos.y + screenRadius > screenHeight);
}

glm::vec3 ModelController::resolveWindowBoundaryCollision(const glm::vec3& oldPosition, const glm::vec3& newPosition, float radius) const {
    if (!enableWindowBoundaries) {
        return newPosition;
    }

    // Preserve the original z-coordinate
    float originalZ = newPosition.z;

    // Project positions to screen space
    glm::mat4 view = camera->GetViewMatrix();
    glm::mat4 projection = glm::perspective(glm::radians(camera->Zoom),
                                          (float)screenWidth / (float)screenHeight,
                                          0.1f, 100.0f);
    glm::mat4 model = glm::mat4(1.0f);
    glm::vec4 viewport = glm::vec4(0, 0, screenWidth, screenHeight);

    glm::vec3 newScreenPos = glm::project(newPosition, model * view, projection, viewport);

    // Calculate accurate screen space radius based on distance from camera
    float distanceFromCamera = glm::length(newPosition - camera->Position);
    float fovRadians = glm::radians(camera->Zoom);
    float screenRadius = (radius * screenHeight) / (2.0f * distanceFromCamera * tan(fovRadians / 2.0f));

    // Add a small buffer to ensure the model stays fully visible
    float buffer = screenRadius * 0.1f;
    screenRadius += buffer;

    // Clamp screen position to boundaries
    glm::vec3 clampedScreenPos = newScreenPos;
    clampedScreenPos.x = glm::clamp(clampedScreenPos.x, screenRadius, (float)screenWidth - screenRadius);
    clampedScreenPos.y = glm::clamp(clampedScreenPos.y, screenRadius, (float)screenHeight - screenRadius);

    // If no clamping occurred, return original position
    if (glm::length(clampedScreenPos - newScreenPos) < 0.1f) {
        return newPosition;
    }

    // Unproject the clamped screen position back to world space
    glm::vec3 worldPos = glm::unProject(clampedScreenPos, model * view, projection, viewport);

    // Maintain the same distance from camera as the new position
    glm::vec3 cameraToNew = newPosition - camera->Position;
    glm::vec3 cameraToWorld = worldPos - camera->Position;
    float originalDistance = glm::length(cameraToNew);

    if (glm::length(cameraToWorld) > 0.001f) {
        worldPos = camera->Position + glm::normalize(cameraToWorld) * originalDistance;
    }

    // Force the z-coordinate to remain at the original value
    worldPos.z = originalZ;

    return worldPos;
}

void ModelController::updateModelMatrix(DraggableModel& model) {
    model.modelMatrix = glm::mat4(1.0f);
    model.modelMatrix = glm::translate(model.modelMatrix, model.position);
    model.modelMatrix = glm::rotate(model.modelMatrix, model.rotation.x, glm::vec3(1.0f, 0.0f, 0.0f));
    model.modelMatrix = glm::rotate(model.modelMatrix, model.rotation.y, glm::vec3(0.0f, 1.0f, 0.0f));
    model.modelMatrix = glm::rotate(model.modelMatrix, model.rotation.z, glm::vec3(0.0f, 0.0f, 1.0f));
    model.modelMatrix = glm::scale(model.modelMatrix, model.scale);
}

int ModelController::pickModel(double mouseX, double mouseY) {
    // Create ray from camera through mouse position
    glm::vec3 rayOrigin = camera->Position;
    glm::vec3 rayDirection = calculateMouseRay(mouseX, mouseY);

    // Test intersection with all models
    int closestModel = -1;
    float closestDistance = std::numeric_limits<float>::max();

    for (int i = 0; i < static_cast<int>(models.size()); ++i) {
        float distance;
        float effectiveRadius = models[i].boundingRadius * glm::length(models[i].scale);

        if (rayIntersectsSphere(rayOrigin, rayDirection, models[i].position, effectiveRadius, distance)) {
            if (distance < closestDistance) {
                closestDistance = distance;
                closestModel = i;
            }
        }
    }

    return closestModel;
}

void ModelController::startDragging(int modelIndex, double mouseX, double mouseY) {
    if (modelIndex < 0 || modelIndex >= static_cast<int>(models.size())) {
        LOG_WARNING("Invalid model index for dragging: " + std::to_string(modelIndex));
        return;
    }

    // Drag operation initiated
    selectedModelIndex = modelIndex;
    dragging = true;
    models[modelIndex].isDragging = true;

    // Set up drag plane (parallel to XY plane, passing through model position)
    dragPlaneNormal = glm::vec3(0.0f, 0.0f, 1.0f);
    dragPlaneDistance = models[modelIndex].position.z;

    // Calculate offset from mouse ray to model center
    glm::vec3 rayOrigin = camera->Position;
    glm::vec3 rayDirection = calculateMouseRay(mouseX, mouseY);

    glm::vec3 intersectionPoint = intersectRayPlane(rayOrigin, rayDirection, dragPlaneNormal, dragPlaneDistance);
    dragOffset = models[modelIndex].position - intersectionPoint;

    lastMousePos = glm::vec2(mouseX, mouseY);
}

void ModelController::updateDragging(double mouseX, double mouseY) {
    if (!dragging || selectedModelIndex < 0) {
        return;
    }

    // Calculate new position based on mouse movement
    glm::vec3 rayOrigin = camera->Position;
    glm::vec3 rayDirection = calculateMouseRay(mouseX, mouseY);

    glm::vec3 intersectionPoint = intersectRayPlane(rayOrigin, rayDirection, dragPlaneNormal, dragPlaneDistance);
    glm::vec3 newPosition = intersectionPoint + dragOffset;

    // Apply collision detection and resolution, but preserve the z-coordinate from the drag plane
    DraggableModel& model = models[selectedModelIndex];
    float effectiveRadius = model.boundingRadius * glm::length(model.scale);

    // Use the calculated new position and preserve the z-coordinate
    glm::vec3 resolvedPosition = newPosition;
    resolvedPosition.z = dragPlaneDistance;  // Force the z-coordinate to remain at the drag plane distance

    // Apply collision detection if needed (currently disabled for testing)
    // Create a temporary position with the original z-coordinate for collision testing
    // glm::vec3 tempOldPosition = model.position;
    // tempOldPosition.z = dragPlaneDistance;  // Use the drag plane z-coordinate
    // resolvedPosition = resolveCollision(tempOldPosition, resolvedPosition, effectiveRadius);
    // resolvedPosition.z = dragPlaneDistance;  // Ensure z-coordinate is preserved

    // Check character-to-character collision
    /*if (enableCharacterCollision) {
        resolvedPosition = resolveCharacterCollision(selectedModelIndex, model.position, resolvedPosition, effectiveRadius);
        // Ensure z-coordinate is preserved after character collision resolution
        resolvedPosition.z = dragPlaneDistance;
    }

    // Also check window boundaries if enabled
    if (enableWindowBoundaries) {
        resolvedPosition = resolveWindowBoundaryCollision(model.position, resolvedPosition, effectiveRadius);
        // Ensure z-coordinate is preserved after boundary collision resolution
        resolvedPosition.z = dragPlaneDistance;
    }*/

    if (resolvedPosition != model.position) {
        model.position = resolvedPosition;
        updateModelMatrix(model);
    }

    lastMousePos = glm::vec2(mouseX, mouseY);
}

void ModelController::stopDragging() {
    dragging = false;
    if (selectedModelIndex >= 0) {
        models[selectedModelIndex].isDragging = false;
    }
}

glm::vec3 ModelController::intersectRayPlane(const glm::vec3& rayOrigin, const glm::vec3& rayDirection,
                                           const glm::vec3& planeNormal, float planeDistance) {
    float denominator = glm::dot(rayDirection, planeNormal);

    LOG_DEBUG("=== RAY-PLANE INTERSECTION DEBUG ===");
    LOG_DEBUG("Ray origin: (" + std::to_string(rayOrigin.x) + ", " + std::to_string(rayOrigin.y) + ", " + std::to_string(rayOrigin.z) + ")");
    LOG_DEBUG("Ray direction: (" + std::to_string(rayDirection.x) + ", " + std::to_string(rayDirection.y) + ", " + std::to_string(rayDirection.z) + ")");
    LOG_DEBUG("Plane normal: (" + std::to_string(planeNormal.x) + ", " + std::to_string(planeNormal.y) + ", " + std::to_string(planeNormal.z) + ")");
    LOG_DEBUG("Plane distance: " + std::to_string(planeDistance));
    LOG_DEBUG("Denominator: " + std::to_string(denominator));

    if (std::abs(denominator) < 1e-6f) {
        // Ray is parallel to plane, return ray origin
        LOG_DEBUG("Ray parallel to plane, returning ray origin");
        return rayOrigin;
    }

    float rayOriginDotNormal = glm::dot(rayOrigin, planeNormal);
    float t = (planeDistance - rayOriginDotNormal) / denominator;
    glm::vec3 intersection = rayOrigin + t * rayDirection;

    LOG_DEBUG("Ray origin dot normal: " + std::to_string(rayOriginDotNormal));
    LOG_DEBUG("t parameter: " + std::to_string(t));
    LOG_DEBUG("Intersection: (" + std::to_string(intersection.x) + ", " + std::to_string(intersection.y) + ", " + std::to_string(intersection.z) + ")");
    LOG_DEBUG("=== END RAY-PLANE INTERSECTION DEBUG ===");

    return intersection;
}
